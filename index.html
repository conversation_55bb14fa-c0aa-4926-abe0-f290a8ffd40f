<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音发布管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: white;
            padding: 10px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo {
            width: 24px;
            height: 24px;
            background: #1890ff;
            border-radius: 4px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 14px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .toolbar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-outline {
            background: white;
            color: #1890ff;
            border: 1px solid #1890ff;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .sidebar {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .sidebar h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
        }

        .filter-group {
            margin-bottom: 20px;
        }

        .filter-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            cursor: pointer;
        }

        .filter-item input[type="radio"] {
            margin-right: 8px;
        }

        .main-content {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }

        th, td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        th {
            background: #fafafa;
            font-weight: 500;
            color: #666;
            font-size: 14px;
        }

        td {
            font-size: 14px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-published {
            background: #f6ffed;
            color: #52c41a;
        }

        .status-draft {
            background: #fff7e6;
            color: #fa8c16;
        }

        .image-placeholder {
            width: 40px;
            height: 40px;
            background: #f0f0f0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
        }

        .action-links {
            display: flex;
            gap: 8px;
        }

        .action-links a {
            color: #1890ff;
            text-decoration: none;
            font-size: 12px;
        }

        .action-links a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
            }
            
            .header {
                padding: 10px;
                flex-direction: column;
                gap: 10px;
            }
            
            .header-right {
                font-size: 12px;
                gap: 10px;
            }
            
            .toolbar {
                justify-content: center;
            }
            
            .btn {
                padding: 6px 12px;
                font-size: 12px;
            }
            
            th, td {
                padding: 8px 12px;
                font-size: 12px;
            }
            
            .sidebar {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            .table-container {
                font-size: 11px;
            }
            
            .image-placeholder {
                width: 30px;
                height: 30px;
            }
            
            .action-links {
                flex-direction: column;
                gap: 4px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-left">
            <div class="logo"></div>
            <span>抖音发布管理</span>
        </div>
        <div class="header-right">
            <span>下载APP</span>
            <span>公司社区</span>
            <span>用户反馈</span>
            <span>客服帮助</span>
            <span>联系客服</span>
            <div class="user-info">
                <span>👤</span>
                <span>152****8012</span>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="toolbar">
            <button class="btn btn-primary">批量上传</button>
            <button class="btn btn-secondary">智能生成</button>
            <button class="btn btn-outline">创建计划</button>
            <button class="btn btn-outline">创建素材</button>
            <button class="btn btn-outline">分析报告</button>
        </div>

        <div class="sidebar">
            <h3>筛选条件</h3>
            <div class="filter-group">
                <div class="filter-item">
                    <input type="radio" name="plan" id="all" checked>
                    <label for="all">计划一</label>
                </div>
                <div class="filter-item">
                    <input type="radio" name="plan" id="plan2">
                    <label for="plan2">计划二</label>
                </div>
                <div class="filter-item">
                    <input type="radio" name="plan" id="plan3">
                    <label for="plan3">计划三</label>
                </div>
                <div class="filter-item">
                    <input type="radio" name="plan" id="plan4">
                    <label for="plan4">计划四</label>
                </div>
                <div class="filter-item">
                    <input type="radio" name="plan" id="plan5">
                    <label for="plan5">计划五</label>
                </div>
            </div>
            <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #f0f0f0;">
                <div style="font-size: 12px; color: #999;">
                    最近更新<br>
                    数据
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>计划名称</th>
                            <th>图片</th>
                            <th>计划文案</th>
                            <th>创建时间</th>
                            <th>进度</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>这是计划名称</td>
                            <td><div class="image-placeholder">+2</div></td>
                            <td>—</td>
                            <td>2024-01-20 21:39:22</td>
                            <td><span class="status-badge status-published">已发布</span></td>
                            <td>
                                <div class="action-links">
                                    <a href="#">编辑</a>
                                    <a href="#">上传视频</a>
                                    <a href="#">文案</a>
                                    <a href="#">删除</a>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>这是计划名称</td>
                            <td><div class="image-placeholder">+2</div></td>
                            <td>已完成</td>
                            <td>2024-01-20 21:39:22</td>
                            <td><span class="status-badge status-published">未发布</span></td>
                            <td>
                                <div class="action-links">
                                    <a href="#">编辑</a>
                                    <a href="#">上传视频</a>
                                    <a href="#">文案</a>
                                    <a href="#">删除</a>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>这是计划名称</td>
                            <td><div class="image-placeholder">+2</div></td>
                            <td>已完成</td>
                            <td>2024-01-20 21:39:22</td>
                            <td><span class="status-badge status-published">未发布</span></td>
                            <td>
                                <div class="action-links">
                                    <a href="#">编辑</a>
                                    <a href="#">上传视频</a>
                                    <a href="#">文案</a>
                                    <a href="#">删除</a>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>这是计划名称</td>
                            <td><div class="image-placeholder">+2</div></td>
                            <td>—</td>
                            <td>2024-01-20 21:39:22</td>
                            <td><span class="status-badge status-draft">未发布</span></td>
                            <td>
                                <div class="action-links">
                                    <a href="#">编辑</a>
                                    <a href="#">上传视频</a>
                                    <a href="#">文案</a>
                                    <a href="#">删除</a>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>这是计划名称</td>
                            <td><div class="image-placeholder">+2</div></td>
                            <td>—</td>
                            <td>2024-01-20 21:39:22</td>
                            <td><span class="status-badge status-draft">未发布</span></td>
                            <td>
                                <div class="action-links">
                                    <a href="#">编辑</a>
                                    <a href="#">上传视频</a>
                                    <a href="#">文案</a>
                                    <a href="#">删除</a>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>这是计划名称</td>
                            <td><div class="image-placeholder">+2</div></td>
                            <td>—</td>
                            <td>2024-01-20 21:39:22</td>
                            <td><span class="status-badge status-draft">未发布</span></td>
                            <td>
                                <div class="action-links">
                                    <a href="#">编辑</a>
                                    <a href="#">上传视频</a>
                                    <a href="#">文案</a>
                                    <a href="#">删除</a>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
